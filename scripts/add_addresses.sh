#!/bin/bash

# Define the base URL to which you want to send the POST request
BASE_URL="https://solana-token-tracker.up.railway.app/wallet/add"

# Define the bearer token for authorization
BEARER_TOKEN="b7d244d3cbc65cdcc12a969bb1862e0ae1b7295a1f30ae1eaff695370204b744"

# Array of wallet addresses
WALLET_ADDRESSES=(
  "v2yNhm4KkC87MdBkvvYiMVP7UdSyNYLrfXrFte8A5a7"
  "4Hq9UkSXjEwDZDBMAo9Lnri7VnbQCoomRAdrT6NGMh1c"
  "8MftALg8mGUBiUDjzWyEZDxa19ernyGMX2kTxkKsoxJD"
  "AmZaQwdMRKNC5JzRUujZfrVFyjmoUuvrRmc6iKnQHEv6"
  "8pzBGC9KkyssMFuckrcZTN52rhMng5ikpqkmQNoKn45V"
  "7dGrdJRYtsNR8UYxZ3TnifXGjGc9eRYLq9sELwYpuuUu"
  "5X4593sntQAyxwciqC87mdyZqVVk1Y9ydUPVUF9nUq7T"
  "Gm16UVXwhB8oaipRTa52qJ36zWeBK4k3CurkT8JGE5VE"
  "9Vw7dyF8WUUcRTGzxfiCfTYjVtPh8867tXpmMeNNuGHZ"
  "j1oAbxxiDUWvoHxEDhWE7THLjEkDQW2cSHYn2vttxTF"
  "j1opmdubY84LUeidrPCsSGskTCYmeJVzds1UWm6nngb"
  "UUAhspPgUdGuXUnokmxERH1VvNGNh1ouN3mfcbfV8yd"
  "j1oeQoPeuEDmjvyMwBmCWexzCQup77kbKKxV59CnYbd"
  "49cb4j7fdofrNxuznX32UvRxstnUvvqexGdquzXLjFGF"
  "7UkAiqXg5PNYrtjNDSgjRMEuXRaYkc5eePBvepGxLRrG"
  "7xPDBVE99pvt1KxrurwDaRQzdFd2tsx49X1YcsgeomkH"
  "PeaZjyrgShDgNjd521FuCUDBZcHvE7fuhPQMHzHcsSA"
)

# Log the start of the script
echo "Starting script at $(date)"
echo "Total wallet addresses to process: ${#WALLET_ADDRESSES[@]}"

# Loop through each wallet address and send a POST request
for WALLET in "${WALLET_ADDRESSES[@]}"; do
  # Define the JSON payload for the current wallet address
  JSON_PAYLOAD="{\"address\": \"$WALLET\"}"

  # Log the wallet address being processed
  echo "Processing wallet address: $WALLET"
  
  # Send the POST request using curl
  RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $BEARER_TOKEN" \
    -d "$JSON_PAYLOAD" \
    $BASE_URL)

  # Log the response from the server
  echo "Response for $WALLET: $RESPONSE"


  # Optional: Print a newline for better readability in the terminal
  echo
done

# Log the completion of the script
echo "Script completed at $(date)"