{"name": "solana-tracker", "version": "0.0.1", "description": "Solana Tracker <PERSON>", "author": "", "private": true, "license": "UNLICENSED", "workspaces": ["apps/*"], "scripts": {"api:build": "pnpm --filter @solana-tracker/api build", "api:start": "pnpm --filter @solana-tracker/api start", "api:dev": "pnpm --filter @solana-tracker/api start:dev", "api:debug": "pnpm --filter @solana-tracker/api start:debug", "api:prod": "pnpm --filter @solana-tracker/api start:prod", "api:test": "pnpm --filter @solana-tracker/api test", "api:test:watch": "pnpm --filter @solana-tracker/api test:watch", "api:test:debug": "pnpm --filter @solana-tracker/api test:debug", "api:test:cov": "pnpm --filter @solana-tracker/api test:cov", "api:test:e2e": "pnpm --filter @solana-tracker/api test:e2e", "api:lint": "pnpm --filter @solana-tracker/api lint", "api:lint:fix": "pnpm --filter @solana-tracker/api lint:fix", "api:format": "pnpm --filter @solana-tracker/api format", "api:clean": "pnpm --filter @solana-tracker/api clean", "format:all": "prettier --write \"apps/**/*.{ts,js,json,md}\"", "lint:all": "eslint \"apps/**/*.{ts,js}\" --fix", "test:all": "pnpm -r test", "build:all": "pnpm -r build", "clean:all": "pnpm -r clean"}, "devDependencies": {"@eslint/js": "^9.18.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "rimraf": "^6.0.1", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}}