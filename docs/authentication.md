# API Authentication

## Overview

The API uses two types of authentication:

- **API Token**: For general API endpoints (wallet management)
- **Helius Webhook Token**: For webhook endpoints from Helius

All endpoints require Bearer token authentication by default.

## Setup

Add both tokens to `.env`:

```bash
API_TOKEN=your-secure-api-token
HELIUS_WEBHOOK_TOKEN=your-helius-webhook-token
```

## Usage

### API Endpoints

```bash
# List wallets
curl -H "Authorization: Bearer your-api-token" http://localhost:3000/wallet

# Add wallet
curl -X POST -H "Authorization: Bearer your-api-token" \
  -H "Content-Type: application/json" \
  -d '{"address":"wallet-address"}' \
  http://localhost:3000/wallet/add
```

### Webhook Endpoints

```bash
# Helius webhook
curl -X POST -H "Authorization: Bearer your-webhook-token" \
  -H "Content-Type: application/json" \
  -d '{"feePayer":"test","tokenTransfers":[],"accountData":[]}' \
  http://localhost:3000/webhook/helius
```

## Adding New Endpoints

### Protected Endpoint (Default)

```typescript
@Controller('example')
export class ExampleController {
  @Get()
  getProtectedData() {
    // Automatically requires API_TOKEN
    return { data: 'protected' };
  }
}
```

### Helius Webhook Endpoint

```typescript
@Controller('webhook')
export class WebhookController {
  @Post('helius')
  @HeliusAuth() // Requires HELIUS_WEBHOOK_TOKEN
  receiveWebhook(@Body() data: any) {
    return { success: true };
  }
}
```

### Public Endpoint

```typescript
@Controller('example')
export class ExampleController {
  @Get('public')
  @Public() // Bypasses all authentication
  getPublicData() {
    return { data: 'public' };
  }
}
```
