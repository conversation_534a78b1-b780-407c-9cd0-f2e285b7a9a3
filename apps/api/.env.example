# Application Configuration
NODE_ENV=development
PORT=3000

# MongoDB Configuration
MONGO_URI=mongodb://localhost:27017
DB_NAME=solana-token-tracker

# Helius Configuration
HELIUS_API_KEY=your-helius-api-key-here

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your-telegram-bot-token-here

# API Security Configuration
API_TOKEN=your-secure-api-token-here
HELIUS_WEBHOOK_TOKEN=your-helius-webhook-token-here

# Logging Configuration
LOG_LEVEL=info

# TODO: Add environment validation schema
# TODO: Add rate limiting configuration
# TODO: Add CORS origins configuration
# TODO: Add monitoring and metrics configuration
