import {
  Injectable,
  Logger,
  OnModuleInit,
  OnApplicationShutdown,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Telegraf } from 'telegraf';
import { WalletService } from '../wallet/wallet.service';
import { TokenService } from '../token/token.service';
import { PublicKey } from '@solana/web3.js';
import {
  WalletExistsError,
  InvalidWalletAddressError,
} from '../wallet/wallet.exceptions';
import { TransactionDataDto } from '../../shared/transaction.dto';
import { NetTokenResult } from '../transaction/transaction.types';

// TODO: ARCHITECTURAL CONCERNS - This service has too many responsibilities
// Split into separate services: TelegramBotService, NotificationService, MessageFormatterService
// Extract message formatting logic into a dedicated MessageBuilder class
// Consider using a message queue for notifications to improve reliability
// Add retry mechanism for failed Telegram API calls
// Implement rate limiting to avoid hitting Telegram API limits
@Injectable()
export class BotService implements OnModuleInit, OnApplicationShutdown {
  private readonly logger = new Logger(BotService.name);
  private bot: Telegraf;

  constructor(
    private readonly configService: ConfigService,
    private readonly walletService: WalletService,
    private readonly tokenService: TokenService,
  ) {
    const token = this.configService.get<string>('TELEGRAM_BOT_TOKEN');
    if (!token) {
      this.logger.error('TELEGRAM_BOT_TOKEN is not configured');
      throw new Error('TELEGRAM_BOT_TOKEN is required');
    }

    this.logger.log('Initializing Telegram bot...');
    this.bot = new Telegraf(token);
    this.setupCommands();
    this.setupMiddleware();
    this.logger.log('Telegram bot configuration completed');
  }

  async onModuleInit() {
    try {
      this.logger.log('Starting Telegram bot...');

      // Launch the bot without awaiting to prevent blocking
      // The launch() method in newer Telegraf versions runs indefinitely
      this.bot
        .launch()
        .then(() => {
          this.logger.log(
            'Telegram bot started successfully and is listening for commands',
          );
        })
        .catch((error) => {
          this.logger.error('Failed to start Telegram bot:', error);
        });

      // Get bot info to verify connection
      const botInfo = await this.bot.telegram.getMe();
      this.logger.log(`Bot Info: @${botInfo.username} (${botInfo.first_name})`);
    } catch (error) {
      this.logger.error('Failed to initialize Telegram bot:', error);
      throw error;
    }
  }

  onApplicationShutdown(signal?: string) {
    this.logger.log(
      `Shutting down Telegram bot... (signal: ${signal || 'unknown'})`,
    );
    try {
      this.bot.stop();
      this.logger.log('Telegram bot stopped gracefully');
    } catch (error) {
      this.logger.error('Error stopping Telegram bot:', error);
    }
  }

  async sendNotificationMessage(
    chatId: number,
    message: string,
  ): Promise<void> {
    try {
      await this.bot.telegram.sendMessage(chatId, message, {
        parse_mode: 'Markdown',
        link_preview_options: { is_disabled: true },
      });
      this.logger.log(`Notification sent to chat ${chatId}`);
    } catch (error) {
      this.logger.error(
        `Failed to send notification to chat ${chatId}:`,
        error,
      );
      throw error;
    }
  }

  // TODO: TECHNICAL DEBT - Hardcoded chat ID should be configurable
  // TODO: RELIABILITY - Add transaction hash/signature to messages for better tracking
  async sendWebhookNotification(
    webhookEvent: TransactionDataDto,
    netTokenResults: NetTokenResult[],
  ): Promise<void> {
    const TELEGRAM_CHAT_ID = **********; // TODO: Move to configuration

    try {
      this.logger.log('Preparing webhook notification message');

      // TODO: MAINTAINABILITY - Extract message building to separate method/class
      let message = `*Transaction Detected*\n\n`;
      message += `**Wallet:** [${webhookEvent.feePayer}](https://solscan.io/account/${webhookEvent.feePayer})\n`;
      message += `**Token Changes:** ${netTokenResults.length}\n\n`;

      // TODO: PERFORMANCE - Process tokens in parallel instead of sequentially
      for (const result of netTokenResults) {
        try {
          const tokenMetadata = await this.tokenService.getTokenMetadata(
            result.mint,
          );

          const tokenName = tokenMetadata?.name || 'Unknown Token';
          const tokenSymbol = tokenMetadata?.symbol || 'N/A';
          const changeType = result.netAmount > 0 ? 'Received' : 'Sent';
          const amount = Math.abs(result.netAmount);

          message += `${changeType}: ${amount} ${tokenSymbol} (${tokenName})\n`;
          message += `Token: [${tokenSymbol}](https://solscan.io/token/${result.mint})\n\n`;
        } catch (error) {
          this.logger.error(
            `Failed to fetch metadata for token ${result.mint}, skipping:`,
            error,
          );

          continue;
        }
      }

      await this.sendNotificationMessage(TELEGRAM_CHAT_ID, message);
      this.logger.log('Webhook notification sent successfully');
    } catch (error) {
      this.logger.error('Failed to send webhook notification:', error);
      throw error;
    }
  }

  private setupMiddleware() {
    // Log all incoming messages
    this.bot.use((ctx, next) => {
      const user = ctx.from;
      const chat = ctx.chat;
      const messageType = ctx.message
        ? 'message'
        : ctx.callbackQuery
          ? 'callback'
          : 'unknown';

      this.logger.log(
        `Incoming ${messageType} from user ${user?.id} (@${user?.username || 'no_username'}) ` +
          `in chat ${chat?.id} (${chat?.type})`,
      );

      if (ctx.message && 'text' in ctx.message) {
        this.logger.log(`Message text: "${ctx.message.text}"`);
      }

      return next();
    });

    // Error handling middleware
    this.bot.catch((err, ctx) => {
      this.logger.error(`Bot error for user ${ctx.from?.id}:`, err);
    });
  }

  private setupCommands() {
    this.bot.start((ctx) => {
      const user = ctx.from;
      this.logger.log(
        `/start command executed by user ${user?.id} (@${user?.username || 'no_username'})`,
      );

      ctx.reply(
        'Welcome to Solana Tracker Bot!\n\n' +
          'Available commands:\n' +
          '/add <wallet_address> - Add wallet to tracking\n' +
          '/list - Show tracked wallets\n' +
          '/remove <wallet_address> - Remove wallet\n' +
          '/help - Show this help message',
      );
    });

    this.bot.help((ctx) => {
      const user = ctx.from;
      this.logger.log(
        `/help command executed by user ${user?.id} (@${user?.username || 'no_username'})`,
      );

      ctx.reply(
        'Available commands:\n' +
          '/add <wallet_address> - Add wallet to tracking\n' +
          '/list - Show tracked wallets\n' +
          '/remove <wallet_address> - Remove wallet\n' +
          '/help - Show this help message',
      );
    });

    this.bot.command('add', async (ctx) => {
      const user = ctx.from;
      const userId = user?.id;
      const username = user?.username || 'no_username';

      try {
        const text = ctx.message.text;
        const parts = text.split(' ');

        this.logger.log(
          `/add command executed by user ${userId} (@${username})`,
        );

        if (parts.length !== 2) {
          this.logger.warn(
            `Invalid /add usage by user ${userId}: missing wallet address`,
          );
          return ctx.reply('Usage: /add <wallet_address>');
        }

        const walletAddress = parts[1];
        this.logger.log(
          `Attempting to add wallet: ${walletAddress} for user ${userId}`,
        );

        // Validate address format
        try {
          new PublicKey(walletAddress);
        } catch {
          this.logger.warn(
            `Invalid wallet address format by user ${userId}: ${walletAddress}`,
          );
          return ctx.reply('Invalid Solana wallet address format');
        }

        const wallet = await this.walletService.addWallet(walletAddress);
        this.logger.log(
          `Wallet successfully added by user ${userId}: ${wallet.address}`,
        );

        ctx.reply(`Wallet added successfully!\nAddress: \`${wallet.address}\``);
      } catch (error) {
        this.logger.error(`Error adding wallet for user ${userId}:`, error);

        if (error instanceof InvalidWalletAddressError) {
          this.logger.warn(`Invalid wallet address error for user ${userId}`);
          ctx.reply('Invalid Solana wallet address');
        } else if (error instanceof WalletExistsError) {
          this.logger.warn(`Wallet already exists error for user ${userId}`);
          ctx.reply('This wallet is already being tracked');
        } else {
          this.logger.error(
            `Unexpected error adding wallet for user ${userId}:`,
            error,
          );
          ctx.reply('Failed to add wallet. Please try again.');
        }
      }
    });

    this.bot.command('list', async (ctx) => {
      const user = ctx.from;
      const userId = user?.id;
      const username = user?.username || 'no_username';

      try {
        this.logger.log(
          `/list command executed by user ${userId} (@${username})`,
        );

        const wallets = await this.walletService.getActiveWallets();
        this.logger.log(
          `Retrieved ${wallets.length} wallets for user ${userId}`,
        );

        if (wallets.length === 0) {
          this.logger.log(`No wallets found for user ${userId}`);
          return ctx.reply('No wallets are currently being tracked');
        }

        const walletList = wallets
          .map(
            (wallet, index) =>
              `${index + 1}. [${wallet.address}](https://solscan.io/account/${wallet.address})`,
          )
          .join('\n');

        this.logger.log(
          `Wallet list sent to user ${userId} (${wallets.length} wallets)`,
        );
        ctx.reply(`Tracked Wallets (${wallets.length}):\n\n${walletList}`, {
          parse_mode: 'Markdown',
        });
      } catch (error) {
        this.logger.error(`Error listing wallets for user ${userId}:`, error);
        ctx.reply('Failed to retrieve wallets. Please try again.');
      }
    });

    this.bot.command('remove', async (ctx) => {
      const user = ctx.from;
      const userId = user?.id;
      const username = user?.username || 'no_username';

      try {
        const text = ctx.message.text;
        const parts = text.split(' ');

        this.logger.log(
          `/remove command executed by user ${userId} (@${username})`,
        );

        if (parts.length !== 2) {
          this.logger.warn(
            `Invalid /remove usage by user ${userId}: missing wallet address`,
          );
          return ctx.reply('Usage: /remove <wallet_address>');
        }

        const walletAddress = parts[1];
        this.logger.log(
          `Attempting to remove wallet: ${walletAddress} for user ${userId}`,
        );

        // Validate address format
        try {
          new PublicKey(walletAddress);
        } catch {
          this.logger.warn(
            `Invalid wallet address format in /remove by user ${userId}: ${walletAddress}`,
          );
          return ctx.reply('Invalid Solana wallet address format');
        }

        // Note: We'd need to add removeWallet method to WalletService
        // For now, just show a placeholder
        this.logger.warn(
          `Remove functionality not implemented - requested by user ${userId} for wallet ${walletAddress}`,
        );
        ctx.reply('Remove functionality not implemented yet');
      } catch (error) {
        this.logger.error(`Error removing wallet for user ${userId}:`, error);
        ctx.reply('Failed to remove wallet. Please try again.');
      }
    });

    // Handle unknown commands - this will only trigger for unhandled commands
    this.bot.hears(/^\/(?!start|help|add|list|remove).*/, (ctx) => {
      const user = ctx.from;
      const userId = user?.id;
      const username = user?.username || 'no_username';
      const command = ctx.message.text;

      this.logger.warn(
        `Unknown command "${command}" from user ${userId} (@${username})`,
      );
      ctx.reply('Unknown command. Type /help for available commands.');
    });
  }
}
