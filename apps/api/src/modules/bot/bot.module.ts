import { Module, forwardRef } from '@nestjs/common';
import { BotService } from './bot.service';
import { WalletModule } from '../wallet/wallet.module';
import { TokenModule } from '../token/token.module';

// TODO: ARCHITECTURAL DEBT - Remove circular dependencies by applying one or more of the following:
// 1. Creating a SharedModule for common services
// 2. Using event-driven architecture instead of direct service calls
// 3. Implementing dependency inversion with interfaces
@Module({
  imports: [forwardRef(() => WalletModule), TokenModule],
  providers: [BotService],
  exports: [BotService],
})
export class BotModule {}
