import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({ collection: 'tracked-wallets' })
export class TrackedWallet {
  @Prop({ required: true, unique: true })
  address!: string;

  @Prop({ default: true })
  isActive!: boolean;

  @Prop({ default: Date.now })
  dateAdded!: Date;

  @Prop()
  lastChecked?: Date;
}

export const TrackedWalletSchema = SchemaFactory.createForClass(TrackedWallet);
