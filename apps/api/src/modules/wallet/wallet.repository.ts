import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { TrackedWallet } from './wallet.schema';

@Injectable()
export class WalletRepository {
  constructor(
    @InjectModel(TrackedWallet.name) private walletModel: Model<TrackedWallet>,
  ) {}

  async addWallet(address: string): Promise<TrackedWallet> {
    const wallet = new this.walletModel({ address });
    return await wallet.save();
  }

  async getWallet(address: string): Promise<TrackedWallet | null> {
    return await this.walletModel.findOne({ address }).exec();
  }

  async getAllWallets(): Promise<TrackedWallet[]> {
    return await this.walletModel.find().exec();
  }

  async getActiveWallets(): Promise<TrackedWallet[]> {
    return await this.walletModel.find({ isActive: true }).exec();
  }

  async updateLastChecked(address: string): Promise<boolean> {
    const result = await this.walletModel.updateOne(
      { address },
      { lastChecked: new Date() },
    );
    return result.modifiedCount > 0;
  }

  async deactivateWallet(address: string): Promise<boolean> {
    const result = await this.walletModel.updateOne(
      { address },
      { isActive: false },
    );
    return result.modifiedCount > 0;
  }

  async removeWallet(address: string): Promise<boolean> {
    const result = await this.walletModel.deleteOne({ address });
    return result.deletedCount > 0;
  }
}
