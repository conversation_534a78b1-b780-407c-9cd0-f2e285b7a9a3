import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
  HttpStatus,
} from '@nestjs/common';
import { Response } from 'express';
import {
  InvalidWalletAddressError,
  WalletNotFoundError,
  WalletExistsError,
  NoWebhooksConfiguredError,
  WebhookFailedError,
  WalletError,
} from './wallet.exceptions';

@Catch(
  InvalidWalletAddressError,
  WalletNotFoundError,
  WalletExistsError,
  NoWebhooksConfiguredError,
  WebhookFailedError,
)
export class WalletExceptionFilter implements ExceptionFilter {
  catch(exception: Error, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    if (exception instanceof InvalidWalletAddressError) {
      const error = new BadRequestException({
        message: exception.message,
        error: WalletError.INVALID_ADDRESS,
        statusCode: HttpStatus.BAD_REQUEST,
        details: { address: exception.message.split(': ')[1] },
      });
      return response.status(error.getStatus()).json(error.getResponse());
    }

    if (exception instanceof WalletNotFoundError) {
      const error = new NotFoundException({
        message: exception.message,
        error: WalletError.WALLET_NOT_FOUND,
        statusCode: HttpStatus.NOT_FOUND,
        details: { address: exception.message.split(': ')[1] },
      });
      return response.status(error.getStatus()).json(error.getResponse());
    }

    if (exception instanceof WalletExistsError) {
      const error = new BadRequestException({
        message: exception.message,
        error: WalletError.WALLET_EXISTS,
        statusCode: HttpStatus.CONFLICT,
        details: { address: exception.message.split(': ')[1] },
      });
      return response.status(error.getStatus()).json(error.getResponse());
    }

    if (exception instanceof NoWebhooksConfiguredError) {
      const error = new InternalServerErrorException({
        message: exception.message,
        error: WalletError.NO_WEBHOOKS,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return response.status(error.getStatus()).json(error.getResponse());
    }

    if (exception instanceof WebhookFailedError) {
      const error = new InternalServerErrorException({
        message: exception.message,
        error: WalletError.WEBHOOK_FAILED,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        details: { address: exception.message.split(' ')[3] },
      });
      return response.status(error.getStatus()).json(error.getResponse());
    }

    // If we get here, it's an unhandled error
    const error = new InternalServerErrorException({
      message: 'An unexpected error occurred',
      error: WalletError.UNEXPECTED_ERROR,
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    });
    return response.status(error.getStatus()).json(error.getResponse());
  }
}
