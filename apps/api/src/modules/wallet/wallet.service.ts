import { Injectable, Logger } from '@nestjs/common';
import { WalletRepository } from './wallet.repository';
import { TrackedWallet } from './wallet.schema';
import { WebhookManagerService } from '../webhook/webhook-manager.service';
import {
  InvalidWalletAddressError,
  WalletNotFoundError,
  WalletExistsError,
  NoWebhooksConfiguredError,
  WebhookFailedError,
} from './wallet.exceptions';
import { PublicKey } from '@solana/web3.js';

@Injectable()
export class WalletService {
  private readonly logger = new Logger(WalletService.name);

  constructor(
    private readonly walletRepository: WalletRepository,
    private readonly webhookManagerService: WebhookManagerService,
  ) {}

  async addWallet(address: string): Promise<TrackedWallet> {
    this.logger.log(`Adding wallet: ${address}`);

    try {
      const existingWallet = await this.walletRepository.getWallet(address);
      if (existingWallet) {
        this.logger.log(`Wallet already exists: ${address}`);
        throw new WalletExistsError(address);
      }

      const wallet = await this.walletRepository.addWallet(address);
      this.logger.log(`Wallet saved to database: ${address}`);

      try {
        await this.addWalletToWebhook(address);
        this.logger.log(`Wallet added to webhook: ${address}`);

        this.logger.log(`Wallet added successfully: ${address}`);
        return wallet;
      } catch (webhookError) {
        this.logger.error(
          `Webhook failed, rolling back database for ${address}:`,
          webhookError,
        );

        let rollbackFailed = false;
        try {
          await this.walletRepository.removeWallet(address);
          this.logger.log(`Rollback successful for ${address}`);
        } catch (rollbackError) {
          this.logger.error(`Rollback failed for ${address}:`, rollbackError);
          rollbackFailed = true;
        }

        throw new WebhookFailedError(
          address,
          rollbackFailed ? 'ROLLBACK_FAILED' : 'ROLLBACK_SUCCESS',
        );
      }
    } catch (error) {
      if (
        error instanceof WalletExistsError ||
        error instanceof NoWebhooksConfiguredError ||
        error instanceof WebhookFailedError
      ) {
        throw error;
      }
      this.logger.error(`Unexpected error adding wallet ${address}:`, error);
      throw error;
    }
  }

  async getAllWallets(): Promise<TrackedWallet[]> {
    return this.walletRepository.getAllWallets();
  }

  async getActiveWallets(): Promise<TrackedWallet[]> {
    return this.walletRepository.getActiveWallets();
  }

  async getWallet(address: string): Promise<TrackedWallet> {
    try {
      new PublicKey(address);
    } catch {
      throw new InvalidWalletAddressError(address);
    }

    const wallet = await this.walletRepository.getWallet(address);
    if (!wallet) {
      throw new WalletNotFoundError(address);
    }

    return wallet;
  }

  private async addWalletToWebhook(walletAddress: string): Promise<void> {
    try {
      const webhooks = this.webhookManagerService.getWebhooks();

      if (webhooks.length === 0) {
        this.logger.error('No webhooks found to add wallet to');
        throw new NoWebhooksConfiguredError();
      }

      // Add to the first webhook (assuming single webhook setup)
      const webhookId = webhooks[0].webhookID;
      await this.webhookManagerService.appendAddressesToWebhook(webhookId, [
        walletAddress,
      ]);
      this.logger.log(`Added wallet ${walletAddress} to webhook ${webhookId}`);
    } catch (error) {
      this.logger.error(
        `Failed to add wallet ${walletAddress} to webhook:`,
        error,
      );
      throw error;
    }
  }
}
