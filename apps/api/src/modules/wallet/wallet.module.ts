import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { WalletRepository } from './wallet.repository';
import { WalletController } from './wallet.controller';
import { WalletService } from './wallet.service';
import { TrackedWallet, TrackedWalletSchema } from './wallet.schema';
import { WebhookModule } from '../webhook/webhook.module';

// TODO: ARCHITECTURAL DEBT - Circular dependency with WebhookModule
// Extract webhook management to a separate service
// Implement domain events for wallet lifecycle changes
// Add wallet validation and sanitization
// Consider implementing soft deletes for audit trail
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: TrackedWallet.name, schema: TrackedWalletSchema },
    ]),
    forwardRef(() => WebhookModule),
  ],
  controllers: [WalletController],
  providers: [WalletRepository, WalletService],
  exports: [WalletRepository, WalletService],
})
export class WalletModule {}
