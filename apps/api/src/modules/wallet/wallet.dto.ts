import { IsS<PERSON>, IsNotEmpty, Valida<PERSON> } from 'class-validator';
import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { PublicKey } from '@solana/web3.js';

@ValidatorConstraint({ name: 'isSolanaAddress', async: false })
export class IsSolanaAddressConstraint implements ValidatorConstraintInterface {
  validate(address: string) {
    try {
      new PublicKey(address);
      return true;
    } catch {
      return false;
    }
  }

  defaultMessage() {
    return 'Invalid Solana wallet address';
  }
}

export class AddWalletDto {
  @IsString()
  @IsNotEmpty()
  @Validate(IsSolanaAddressConstraint)
  address!: string;
}
