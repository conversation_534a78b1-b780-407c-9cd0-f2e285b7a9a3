import {
  ConflictException,
  InternalServerErrorException,
  HttpStatus,
} from '@nestjs/common';

export enum WalletError {
  WALLET_EXISTS = 'WALLET_EXISTS',
  NO_WEBHOOKS = 'NO_WEBHOOKS',
  WEBHOOK_FAILED = 'WEBHOOK_FAILED',
  UNEXPECTED_ERROR = 'UNEXPECTED_ERROR',
  INVALID_ADDRESS = 'INVALID_ADDRESS',
  WALLET_NOT_FOUND = 'WALLET_NOT_FOUND',
}

// Domain exceptions
export class InvalidWalletAddressError extends Error {
  constructor(address: string) {
    super(`Invalid Solana wallet address: ${address}`);
    this.name = 'InvalidWalletAddressError';
  }
}

export class WalletNotFoundError extends Error {
  constructor(address: string) {
    super(`Wallet not found: ${address}`);
    this.name = 'WalletNotFoundError';
  }
}

export class WalletExistsError extends Error {
  constructor(address: string) {
    super(`Wallet already exists: ${address}`);
    this.name = 'WalletExistsError';
  }
}

export class NoWebhooksConfiguredError extends Error {
  constructor() {
    super('No webhooks configured for wallet tracking');
    this.name = 'NoWebhooksConfiguredError';
  }
}

export class WebhookFailedError extends Error {
  constructor(address: string, rollbackStatus: string) {
    super(
      `Failed to add wallet ${address} to webhook system. Rollback ${rollbackStatus}`,
    );
    this.name = 'WebhookFailedError';
  }
}

// HTTP exceptions (for controller layer)
export class WalletExistsException extends ConflictException {
  constructor(address: string) {
    super({
      message: `Wallet with address ${address} is already being tracked`,
      error: WalletError.WALLET_EXISTS,
      statusCode: HttpStatus.CONFLICT,
      details: { address },
    });
  }
}

export class NoWebhooksConfiguredException extends InternalServerErrorException {
  constructor() {
    super({
      message: 'No webhooks configured for wallet tracking',
      error: WalletError.NO_WEBHOOKS,
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    });
  }
}

export class WebhookFailedException extends InternalServerErrorException {
  constructor(address: string, rollbackStatus: string) {
    super({
      message: `Failed to add wallet ${address} to webhook system`,
      error: WalletError.WEBHOOK_FAILED,
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      details: {
        rollbackStatus:
          rollbackStatus === 'ROLLBACK_FAILED' ? 'failed' : 'successful',
        walletAddress: address,
      },
    });
  }
}

export class UnexpectedWalletException extends InternalServerErrorException {
  constructor(address: string) {
    super({
      message: `Failed to add wallet ${address} due to unexpected error`,
      error: WalletError.UNEXPECTED_ERROR,
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      details: {
        walletAddress: address,
      },
    });
  }
}
