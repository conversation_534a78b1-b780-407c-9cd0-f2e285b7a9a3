import { Module, forwardRef } from '@nestjs/common';
import { WebhookController } from './webhook.controller';
import { WebhookManagerService } from './webhook-manager.service';
import { WebhookProcessorService } from './webhook-processor.service';
import { TransactionModule } from '../transaction/transaction.module';
import { TokenModule } from '../token/token.module';
import { BotModule } from '../bot/bot.module';

// TODO: ARCHITECTURAL DEBT - Circular dependency with BotModule
// Consider using EventEmitter or message queue for notifications
// Implement webhook signature validation for security
// Add webhook retry mechanism with exponential backoff
@Module({
  imports: [TransactionModule, TokenModule, forwardRef(() => BotModule)],
  controllers: [WebhookController],
  providers: [WebhookManagerService, WebhookProcessorService],
  exports: [WebhookManagerService, WebhookProcessorService],
})
export class WebhookModule {}
