import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Helius, type Webhook } from 'helius-sdk';
import { HeliusWebhookConfig } from './webhook.types';

@Injectable()
export class WebhookManagerService implements OnModuleInit {
  private readonly logger = new Logger(WebhookManagerService.name);
  private helius: Helius;
  private webhooks: Map<string, Webhook> = new Map();

  constructor(private readonly configService: ConfigService) {
    const apiKey = this.configService.get<string>('HELIUS_API_KEY');
    if (!apiKey) {
      throw new Error('HELIUS_API_KEY is not configured');
    }
    this.helius = new Helius(apiKey);
  }

  async onModuleInit(): Promise<void> {
    await this.initialize();
  }

  private async initialize(): Promise<void> {
    this.logger.log('Initializing Helius webhook service...');

    try {
      const existingWebhooks = await this.helius.getAllWebhooks();

      for (const webhook of existingWebhooks) {
        this.webhooks.set(webhook.webhookID, webhook);
      }

      this.logger.log(`Found ${existingWebhooks.length} existing webhooks`);
    } catch (error) {
      this.logger.error('Error fetching existing webhooks:', error);
    }
  }

  async createWebhook(config: HeliusWebhookConfig): Promise<Webhook> {
    this.logger.log('Creating webhook...');

    try {
      const webhook = await this.helius.createWebhook({
        accountAddresses: config.accountAddresses,
        transactionTypes: config.transactionTypes,
        webhookURL: config.webhookURL,
      });

      this.webhooks.set(webhook.webhookID, webhook);
      this.logger.log(`Webhook created with ID: ${webhook.webhookID}`);

      return webhook;
    } catch (error) {
      this.logger.error('Error creating webhook:', error);
      throw error;
    }
  }

  async deleteWebhook(webhookID: string): Promise<void> {
    this.logger.log(`Deleting webhook with ID: ${webhookID}`);

    try {
      await this.helius.deleteWebhook(webhookID);
      this.webhooks.delete(webhookID);
      this.logger.log(`Webhook ${webhookID} deleted successfully`);
    } catch (error) {
      this.logger.error(`Error deleting webhook ${webhookID}:`, error);
      throw error;
    }
  }

  getWebhook(webhookID: string): Webhook | undefined {
    return this.webhooks.get(webhookID);
  }

  getWebhooks(): Webhook[] {
    return Array.from(this.webhooks.values());
  }

  async appendAddressesToWebhook(
    webhookID: string,
    addresses: string[],
  ): Promise<void> {
    this.logger.log(
      `Appending ${addresses.length} addresses to webhook ${webhookID}`,
    );

    try {
      await this.helius.appendAddressesToWebhook(webhookID, addresses);
      this.logger.log(
        `Successfully appended addresses to webhook ${webhookID}`,
      );
    } catch (error) {
      this.logger.error(
        `Error appending addresses to webhook ${webhookID}:`,
        error,
      );
      throw error;
    }
  }

  async removeAddressesFromWebhook(
    webhookID: string,
    addresses: string[],
  ): Promise<void> {
    this.logger.log(
      `Removing ${addresses.length} addresses from webhook ${webhookID}`,
    );

    try {
      await this.helius.removeAddressesFromWebhook(webhookID, addresses);
      this.logger.log(
        `Successfully removed addresses from webhook ${webhookID}`,
      );
    } catch (error) {
      this.logger.error(
        `Error removing addresses from webhook ${webhookID}:`,
        error,
      );
      throw error;
    }
  }
}
