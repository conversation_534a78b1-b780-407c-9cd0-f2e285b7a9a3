import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { WebhookProcessorService } from './webhook-processor.service';
import { TransactionDataDto } from '../../shared/transaction.dto';
import { HeliusAuth } from '../../common/decorators/helius-auth.decorator';

// TODO: Consider implementing webhook queue for high-volume processing

@Controller('webhook')
export class WebhookController {
  private readonly logger = new Logger(WebhookController.name);

  constructor(private readonly processorService: WebhookProcessorService) {}

  @Post('helius')
  @HeliusAuth()
  @HttpCode(HttpStatus.OK)
  async receiveWebhookEvent(@Body() webhookEvents: TransactionDataDto[]) {
    this.logger.log(
      `Received webhook events: ${webhookEvents.length} transactions`,
    );

    try {
      // Process each transaction in the webhook payload
      for (const webhookEvent of webhookEvents) {
        await this.processorService.processEvent(webhookEvent);
      }
      return { success: true, processed: webhookEvents.length };
    } catch (error) {
      this.logger.error('Error processing webhook events:', error);
      throw error;
    }
  }
}
