import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { WebhookProcessorService } from './webhook-processor.service';
import { TransactionDataDto } from '../../shared/transaction.dto';
import { HeliusAuth } from '../../common/decorators/helius-auth.decorator';

// TODO: Consider implementing webhook queue for high-volume processing

@Controller('webhook')
export class WebhookController {
  private readonly logger = new Logger(WebhookController.name);

  constructor(private readonly processorService: WebhookProcessorService) {}

  @Post('helius')
  @HeliusAuth()
  @HttpCode(HttpStatus.OK)
  async receiveWebhookEvent(@Body() webhookEvent: TransactionDataDto) {
    const startTime = Date.now();
    this.logger.log(
      `[POST /webhook/helius] Request received - FeePayer: ${webhookEvent.feePayer}, TokenTransfers: ${webhookEvent.tokenTransfers.length}, AccountData: ${webhookEvent.accountData.length}`,
    );

    try {
      await this.processorService.processEvent(webhookEvent);
      const processingTime = Date.now() - startTime;
      this.logger.log(
        `[POST /webhook/helius] Success - Processed in ${processingTime}ms`,
      );
      return { success: true };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(
        `[POST /webhook/helius] Error after ${processingTime}ms - FeePayer: ${webhookEvent.feePayer}:`,
        error,
      );
      throw error;
    }
  }
}
