import { Injectable, Logger } from '@nestjs/common';
import { TransactionService } from '../transaction/transaction.service';
import { TokenService } from '../token/token.service';
import { BotService } from '../bot/bot.service';
import { TransactionDataDto } from '../../shared/transaction.dto';

// TODO: ARCHITECTURAL CONCERNS - This service is doing too much
// Split responsibilities: EventProcessor, NotificationDispatcher, MetadataEnricher
// Add event sourcing to track all processed webhook events
// Implement idempotency to handle duplicate webhook deliveries
// Add metrics and monitoring for webhook processing performance
@Injectable()
export class WebhookProcessorService {
  private readonly logger = new Logger(WebhookProcessorService.name);

  constructor(
    private readonly transactionService: TransactionService,
    private readonly tokenService: TokenService,
    private readonly botService: BotService,
  ) {}

  async processEvent(webhookEvent: TransactionDataDto): Promise<void> {
    this.logger.log(
      'Processing webhook event:',
      JSON.stringify(webhookEvent, null, 2),
    );

    try {
      const netTokenResults =
        this.transactionService.calculateNetTokenResults(webhookEvent);

      this.logger.log(
        'Net token results:',
        JSON.stringify(netTokenResults, null, 2),
      );

      for (const result of netTokenResults) {
        try {
          const tokenMetadata = await this.tokenService.getTokenMetadata(
            result.mint,
          );

          this.logger.debug(
            `Token metadata for ${result.mint}:`,
            JSON.stringify(tokenMetadata, null, 2),
          );
        } catch (error) {
          this.logger.error(
            `Failed to fetch metadata for token ${result.mint}:`,
            error,
          );
        }
      }

      // TODO: RELIABILITY - Use message queue instead of fire-and-forget
      // TODO: OBSERVABILITY - Add structured logging with correlation IDs
      // TODO: RESILIENCE - Implement circuit breaker pattern for external services
      this.botService
        .sendWebhookNotification(webhookEvent, netTokenResults)
        .catch((error: unknown) =>
          this.logger.error(
            'Failed to send Telegram notification:',
            error instanceof Error ? error.message : String(error),
          ),
        );
    } catch (error) {
      this.logger.error('Error processing transaction:', error);
      throw error;
    }
  }
}
