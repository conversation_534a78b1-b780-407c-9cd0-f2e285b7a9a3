import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { <PERSON><PERSON> } from 'helius-sdk';
import { Token } from './token.schema';
import { TokenRepository } from './token.repository';

@Injectable()
export class TokenService {
  private readonly logger = new Logger(TokenService.name);
  private readonly helius: Helius;
  private readonly metadataCache = new Map<string, Token>();

  constructor(
    private readonly tokenRepository: TokenRepository,
    private readonly configService: ConfigService,
  ) {
    const apiKey = this.configService.get<string>('HELIUS_API_KEY');
    if (!apiKey) {
      throw new Error('HELIUS_API_KEY is not configured');
    }
    this.helius = new Helius(apiKey);
  }

  public async getTokenMetadata(mintAddress: string): Promise<Token> {
    if (this.metadataCache.has(mintAddress)) {
      return this.metadataCache.get(mintAddress)!;
    }

    const cachedMetadata = await this.tokenRepository.getToken(mintAddress);
    if (cachedMetadata) {
      this.metadataCache.set(mintAddress, cachedMetadata);
      return cachedMetadata;
    }

    try {
      const metadata = await this.fetchMetadata(mintAddress);

      await this.tokenRepository.upsertToken(metadata);
      this.metadataCache.set(mintAddress, metadata);

      return metadata;
    } catch (error) {
      this.logger.error(
        `Error fetching metadata for token ${mintAddress}`,
        error,
      );

      throw error;
    }
  }

  private async fetchMetadata(mintAddress: string): Promise<Token> {
    try {
      this.logger.log(`Fetching metadata for token ${mintAddress}`);

      const asset = await this.helius.rpc.getAsset({
        id: mintAddress,
      });

      return {
        mint: mintAddress,
        symbol: asset.content?.metadata?.symbol,
        name: asset.content?.metadata?.name,
        image: asset.content?.links?.image,
        jsonUri: asset.content?.json_uri,
      };
    } catch (error) {
      this.logger.error(
        `Error fetching metadata for token ${mintAddress}`,
        error,
      );

      throw error;
    }
  }
}
