import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Token } from './token.schema';

@Injectable()
export class TokenRepository {
  constructor(
    @InjectModel(Token.name)
    private tokenModel: Model<Token>,
  ) {}

  async addToken(tokenData: Partial<Token>): Promise<Token> {
    const token = new this.tokenModel(tokenData);
    return await token.save();
  }

  async getToken(mint: string): Promise<Token | null> {
    return await this.tokenModel.findOne({ mint }).exec();
  }

  async getAllTokens(): Promise<Token[]> {
    return await this.tokenModel.find().exec();
  }

  async updateToken(
    mint: string,
    updateData: Partial<Token>,
  ): Promise<Token | null> {
    return await this.tokenModel
      .findOneAndUpdate({ mint }, updateData, { new: true })
      .exec();
  }

  async removeToken(mint: string): Promise<boolean> {
    const result = await this.tokenModel.deleteOne({ mint });
    return result.deletedCount > 0;
  }

  async upsertToken(tokenData: Token): Promise<Token> {
    return await this.tokenModel
      .findOneAndUpdate({ mint: tokenData.mint }, tokenData, {
        new: true,
        upsert: true,
      })
      .exec();
  }
}
