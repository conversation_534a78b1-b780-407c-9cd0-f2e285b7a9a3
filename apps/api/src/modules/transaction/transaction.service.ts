import { Injectable } from '@nestjs/common';
import { TransactionDataDto } from '../../shared/transaction.dto';
import { NetTokenResult } from './transaction.types';

@Injectable()
export class TransactionService {
  calculateNetTokenResults(tx: TransactionDataDto): NetTokenResult[] {
    const wallet = tx.feePayer;
    const tokenChanges = tx.tokenTransfers.reduce((acc, transfer) => {
      const { mint, tokenAmount, fromUserAccount, toUserAccount } = transfer;
      if (!acc.has(mint)) acc.set(mint, 0);

      const current = acc.get(mint)!;
      if (fromUserAccount === wallet) {
        acc.set(mint, current - tokenAmount);
      }
      if (toUserAccount === wallet) {
        acc.set(mint, current + tokenAmount);
      }

      return acc;
    }, new Map<string, number>());

    return Array.from(tokenChanges, ([mint, netAmount]) => ({
      mint,
      netAmount,
    }));
  }
}
