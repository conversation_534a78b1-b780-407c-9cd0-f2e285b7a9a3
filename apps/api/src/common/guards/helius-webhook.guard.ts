import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';

// TODO: Implement webhook signature validation using HMAC

@Injectable()
export class HeliusWebhookGuard implements CanActivate {
  private readonly logger = new Logger(HeliusWebhookGuard.name);

  constructor(private readonly configService: ConfigService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const { method, url, ip } = request;
    const userAgent = request.get('User-Agent') || 'Unknown';
    const authHeader = request.headers.authorization;

    // Log complete request details
    this.logger.log(
      `[${method}] ${url} - Helius webhook request - IP: ${ip} - UserAgent: ${userAgent}`,
    );

    // Log request headers (sanitize sensitive data)
    const sanitizedHeaders = { ...request.headers };

    this.logger.log(
      `[${method}] ${url} - Request Headers: ${JSON.stringify(sanitizedHeaders, null, 2)}`,
    );

    // Log complete request body
    if (request.body) {
      this.logger.log(
        `[${method}] ${url} - Request Body: ${JSON.stringify(request.body, null, 2)}`,
      );
    } else {
      this.logger.log(`[${method}] ${url} - Request Body: [EMPTY]`);
    }

    // TODO: Add IP whitelist validation for Helius webhook sources
    // TODO: Implement request rate limiting per webhook source
    if (!authHeader?.startsWith('Bearer ')) {
      this.logger.warn(
        `[${method}] ${url} - Helius webhook request without Bearer token - IP: ${ip}`,
      );
      throw new UnauthorizedException('Helius webhook token required');
    }

    const token = authHeader.substring(7);
    const validToken = this.configService.get<string>('HELIUS_WEBHOOK_TOKEN');

    if (!validToken) {
      this.logger.error(
        `[${method}] ${url} - HELIUS_WEBHOOK_TOKEN not configured - IP: ${ip}`,
      );
      throw new UnauthorizedException(
        'Helius webhook authentication not configured',
      );
    }

    // TODO: Use constant-time comparison to prevent timing attacks
    if (token !== validToken) {
      this.logger.warn(
        `[${method}] ${url} - Invalid Helius webhook token - IP: ${ip}`,
      );
      throw new UnauthorizedException('Invalid webhook token');
    }

    this.logger.log(
      `[${method}] ${url} - Helius webhook authenticated successfully - IP: ${ip}`,
    );
    // TODO: Add webhook payload validation and sanitization
    return true;
  }
}
