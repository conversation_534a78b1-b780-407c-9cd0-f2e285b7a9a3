import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';
import { IS_HELIUS_AUTH_KEY } from '../decorators/helius-auth.decorator';

// TODO: Add audit logging for all authentication attempts with IP tracking
// TODO: Consider implementing JWT tokens with expiration for better security

@Injectable()
export class ApiTokenGuard implements CanActivate {
  private readonly logger = new Logger(ApiTokenGuard.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly reflector: Reflector,
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      this.logger.debug('Public endpoint accessed - skipping authentication');
      return true;
    }

    const isHeliusAuth = this.reflector.getAllAndOverride<boolean>(
      IS_HELIUS_AUTH_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (isHeliusAuth) {
      this.logger.debug(
        'Helius webhook endpoint accessed - skipping API token authentication',
      );
      return true;
    }

    const request = context.switchToHttp().getRequest<Request>();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      // TODO: Add request fingerprinting for security monitoring
      this.logger.warn('API request without token');
      throw new UnauthorizedException('API token is required');
    }

    const apiToken = this.configService.get<string>('API_TOKEN');
    if (!apiToken) {
      this.logger.error('API_TOKEN is not configured');
      throw new UnauthorizedException('API authentication not configured');
    }

    // TODO: Use constant-time comparison to prevent timing attacks
    if (token !== apiToken) {
      this.logger.warn('API request with invalid token');
      throw new UnauthorizedException('Invalid API token');
    }

    this.logger.debug('API request authenticated successfully');
    return true;
  }

  private extractTokenFromHeader(request: Request): string | null {
    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    return authHeader.substring(7);
  }
}
