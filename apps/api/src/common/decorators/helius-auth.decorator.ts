import { UseGuards, SetMetadata } from '@nestjs/common';
import { HeliusWebhookGuard } from '../guards/helius-webhook.guard';

/**
 * Decorator to protect Helius webhook endpoints with webhook-specific token
 *
 * Usage:
 * @HeliusAuth()
 * @Post('helius')
 * receiveWebhook() { ... }
 *
 * Clients must include the Helius webhook token as:
 * - Authorization: Bearer <helius-webhook-token>
 */
export const IS_HELIUS_AUTH_KEY = 'isHeliusAuth';
export const HeliusAuth = () => {
  return (
    target: any,
    propertyKey?: string,
    descriptor?: PropertyDescriptor,
  ) => {
    SetMetadata(IS_HELIUS_AUTH_KEY, true)(target, propertyKey, descriptor);
    UseGuards(HeliusWebhookGuard)(target, propertyKey, descriptor);
  };
};
