import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request, Response } from 'express';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<unknown> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    const { method, url, ip } = request;
    const userAgent = request.get('User-Agent') || '';
    const startTime = Date.now();

    // Log incoming request
    this.logger.log(`[${method}] ${url} - IP: ${ip} - UserAgent: ${userAgent}`);

    return next.handle().pipe(
      tap({
        next: () => {
          const responseTime = Date.now() - startTime;
          const { statusCode } = response;
          this.logger.log(
            `[${method}] ${url} - ${statusCode} - ${responseTime}ms - IP: ${ip}`,
          );
        },
        error: (error: unknown) => {
          const responseTime = Date.now() - startTime;
          const statusCode = this.getErrorStatus(error);
          const errorMessage = this.getErrorMessage(error);
          this.logger.error(
            `[${method}] ${url} - ${statusCode} - ${responseTime}ms - IP: ${ip} - Error: ${errorMessage}`,
          );
        },
      }),
    );
  }

  private getErrorStatus(error: unknown): number {
    if (error && typeof error === 'object' && 'status' in error) {
      const status = error.status;
      return typeof status === 'number' ? status : 500;
    }
    return 500;
  }

  private getErrorMessage(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    if (error && typeof error === 'object' && 'message' in error) {
      return String(error.message);
    }
    return 'Unknown error';
  }
}
