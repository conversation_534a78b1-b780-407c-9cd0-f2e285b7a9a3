import {
  IsString,
  <PERSON><PERSON><PERSON>ber,
  IsArray,
  ValidateNested,
  IsOptional,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';

export class TokenTransferDto {
  @IsString()
  fromUserAccount!: string;

  @IsString()
  toUserAccount!: string;

  @IsString()
  mint!: string;

  @IsNumber()
  tokenAmount!: number;

  @IsOptional()
  @IsString()
  fromTokenAccount?: string;

  @IsOptional()
  @IsString()
  toTokenAccount?: string;

  @IsOptional()
  @IsString()
  tokenStandard?: string;
}

export class AccountDataDto {
  @IsString()
  account!: string;

  @IsNumber()
  nativeBalanceChange!: number;

  @IsOptional()
  @IsArray()
  tokenBalanceChanges?: unknown[];
}

export class TransactionDataDto {
  @IsString()
  feePayer!: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TokenTransferDto)
  tokenTransfers!: TokenTransferDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AccountDataDto)
  accountData!: AccountDataDto[];

  // Additional Helius webhook fields (optional)
  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsObject()
  events?: Record<string, unknown>;

  @IsOptional()
  @IsNumber()
  fee?: number;

  @IsOptional()
  @IsArray()
  instructions?: unknown[];

  @IsOptional()
  @IsArray()
  nativeTransfers?: unknown[];

  @IsOptional()
  @IsString()
  signature?: string;

  @IsOptional()
  @IsNumber()
  slot?: number;

  @IsOptional()
  @IsString()
  source?: string;

  @IsOptional()
  @IsNumber()
  timestamp?: number;

  @IsOptional()
  transactionError?: unknown;

  @IsOptional()
  @IsString()
  type?: string;
}

// Helius sends an array of transactions
export class HeliusWebhookDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TransactionDataDto)
  transactions!: TransactionDataDto[];
}
