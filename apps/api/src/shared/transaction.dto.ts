import { Is<PERSON>tring, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>y, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class TokenTransferDto {
  @IsString()
  fromUserAccount!: string;

  @IsString()
  toUserAccount!: string;

  @IsString()
  mint!: string;

  @IsNumber()
  tokenAmount!: number;
}

export class AccountDataDto {
  @IsString()
  account!: string;

  @IsNumber()
  nativeBalanceChange!: number;
}

export class TransactionDataDto {
  @IsString()
  feePayer!: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TokenTransferDto)
  tokenTransfers!: TokenTransferDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AccountDataDto)
  accountData!: AccountDataDto[];
}
