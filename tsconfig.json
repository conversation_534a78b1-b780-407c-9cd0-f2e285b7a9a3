{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": true, "strictBindCallApply": false, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "noPropertyAccessFromIndexSignature": true}}